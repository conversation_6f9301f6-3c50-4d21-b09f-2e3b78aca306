/* 添加账号功能样式 */

/* 头部按钮组 */
.header-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 添加账号模态框 */
.add-account-modal-content {
  width: 90%;
  max-width: 600px;
  max-height: 95vh;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.add-account-modal-content .api-modal-body {
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-height: 0; /* 允许flex子项收缩 */
}

/* 登录方式选择器 */
.login-method-selector {
  margin-bottom: 16px;
}

.method-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 16px;
}

.method-tab {
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  white-space: nowrap;
  min-width: 0;
}

.method-tab:hover {
  color: var(--primary-color);
  background: var(--bg-tertiary);
}

.method-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: var(--bg-secondary);
}

/* 设备版本选择器 */
.device-version-selector {
  margin-bottom: 16px;
}

.device-version-selector label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary);
}

.device-version-selector select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.device-version-selector select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 设备信息配置区域 */
.device-info-section {
  margin-bottom: 16px;
  padding: 16px;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-large);
  border: 1px solid var(--border-light);
}

.device-info-section .section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 16px;
  color: var(--text-primary);
  padding-bottom: 6px;
  border-bottom: 1px solid var(--border-light);
}

.device-info-section .section-title i {
  color: var(--primary-color);
  font-size: 18px;
}

/* 登录内容区域 */
.login-content {
  animation: fadeIn 0.3s ease;
}

/* 二维码容器 */
.qrcode-container {
  text-align: center;
}

/* 二维码加载遮罩 */
.qrcode-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: var(--radius-medium);
}

.qrcode-loading-overlay i {
  font-size: 24px;
  color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.qrcode-status {
  margin-bottom: 16px;
  padding: 10px 14px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-small);
  color: var(--text-secondary);
  font-size: 14px;
}

.qrcode-status.success {
  background: #f6ffed;
  color: var(--success-color);
  border: 1px solid #b7eb8f;
}

.qrcode-status.error {
  background: #fff2f0;
  color: var(--error-color);
  border: 1px solid #ffccc7;
}

.qrcode-status.warning {
  background: #fffbe6;
  color: var(--warning-color);
  border: 1px solid #ffe58f;
}

.qrcode-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 登录表单 */
.login-form {
  max-width: 400px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary);
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  font-family: "Inter", "Nunito Sans", "SF Pro Rounded", sans-serif;
  font-weight: 400;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-focus);
}

.form-control:disabled {
  background: var(--bg-secondary);
  color: var(--text-tertiary);
  cursor: not-allowed;
}

/* 表单验证样式 */
.form-control.is-invalid {
  border-color: var(--error-color);
  box-shadow: 0 0 0 2px var(--error-light);
}

.form-control.is-valid {
  border-color: var(--success-color);
  box-shadow: 0 0 0 2px var(--success-light);
}

.validation-error {
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.form-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
  display: block;
}

.text-muted {
  color: var(--text-secondary) !important;
}

.text-danger {
  color: #dc3545 !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

/* 输入组样式 */
.input-group {
  display: flex;
  align-items: stretch;
  width: 100%;
}

.input-group .form-control {
  flex: 1;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

.input-group .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 1px solid var(--border-color);
  padding: 8px 12px;
  font-size: 12px;
  min-width: auto;
  white-space: nowrap;
}

.input-group .btn:hover {
  background-color: var(--bg-secondary);
  border-color: var(--primary-color);
}

.input-group .btn:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.btn-outline-secondary {
  color: var(--text-secondary);
  border-color: var(--border-color);
  background-color: transparent;
}

.btn-outline-secondary:hover {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

.btn-sm {
  padding: 6px 10px;
  font-size: 12px;
  line-height: 1.4;
}

/* 设备信息操作区域 */
.device-info-actions {
  text-align: center;
  margin: 12px 0;
  padding: 10px;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-medium);
  border: 1px dashed var(--border-color);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: transparent;
}

.btn-outline-primary:hover {
  color: white;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-actions {
  text-align: center;
  margin-top: 20px;
}

/* 按钮样式 - 继承全局简约风格 */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  color: white;
}

.btn-secondary {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.btn-secondary:hover:not(:disabled) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* 二维码显示容器 */
.qrcode-display-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-medium);
  box-sizing: border-box;
}

/* 二维码图片包装器 */
.qrcode-image-wrapper {
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-small);
  overflow: hidden;
  background: white;
}

.qrcode-image-wrapper img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 二维码提示信息 */
.qrcode-tips {
  text-align: center;
  color: var(--text-secondary);
}

.qrcode-tips i {
  font-size: 20px;
  margin-bottom: 8px;
  color: var(--primary-color);
}

.qrcode-tips p {
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

/* 二维码状态样式 */
.qrcode-status {
  text-align: center;
  margin: 12px 0;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.qrcode-status.success {
  color: var(--success-color);
}

.qrcode-status.error {
  color: var(--error-color);
}

/* 二维码操作链接样式 */
.qrcode-action-link {
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 4px;
  border-radius: var(--radius-small);
}

.qrcode-action-link:hover {
  background: rgba(24, 144, 255, 0.1);
  border-bottom-color: var(--primary-color);
  transform: translateY(-1px);
}

.qrcode-action-link:active {
  transform: translateY(0);
}

.qrcode-action-link:disabled,
.qrcode-action-link.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  color: var(--text-tertiary);
  border-bottom-color: transparent;
  transform: none;
}

/* 二维码操作按钮区域 */
.qrcode-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 12px;
}

/* 二维码错误状态 */
.qrcode-error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: var(--error-color);
  text-align: center;
  padding: 16px;
}

.qrcode-error i {
  font-size: 48px;
  opacity: 0.6;
}

.qrcode-error h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.qrcode-error p {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}
