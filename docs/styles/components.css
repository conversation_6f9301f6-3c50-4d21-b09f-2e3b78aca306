/* 通用组件样式 */

/* 统一简约按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: transparent;
  color: var(--text-primary);
  font-size: 13px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-primary:hover {
  background: rgba(24, 144, 255, 0.1);
  border-color: var(--primary-hover);
  color: var(--primary-hover);
}

.btn-success {
  border-color: var(--success-color);
  color: var(--success-color);
}

.btn-success:hover {
  background: rgba(82, 196, 26, 0.1);
  border-color: #73d13d;
  color: #73d13d;
}

.btn-warning {
  border-color: var(--warning-color);
  color: var(--warning-color);
}

.btn-warning:hover {
  background: var(--warning-light);
  border-color: var(--warning-hover);
  color: var(--warning-hover);
}

.btn-danger {
  border-color: var(--error-color);
  color: var(--error-color);
}

.btn-danger:hover {
  background: var(--error-light);
  border-color: var(--error-hover);
  color: var(--error-hover);
}

.btn-secondary {
  border-color: #d9d9d9;
  color: #666;
}

.btn-secondary:hover {
  background: #f5f5f5;
  border-color: #bfbfbf;
  color: #333;
}

.btn-info {
  border-color: #1890ff;
  color: #1890ff;
}

.btn-info:hover {
  background: rgba(24, 144, 255, 0.1);
  border-color: #40a9ff;
  color: #40a9ff;
}

/* 通用模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
  box-sizing: border-box;
}

.modal-overlay.show {
  display: flex;
}

.modal-container {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  width: 100%;
  max-width: 480px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.modal-title .text-warning {
  color: #fa8c16;
  font-size: 20px;
}

.modal-close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: var(--radius-small);
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--border-light);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 模态框按钮样式 - 继承统一简约风格 */
.modal-footer .btn {
  /* 继承全局按钮样式，无需重复定义 */
}

.modal-footer .btn i {
  font-size: 12px;
}

/* 账号详情页面的操作按钮 */
.account-detail-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 账号详情页面的按钮继承全局简约样式 */

/* 已移除，使用统一的按钮样式 */

.account-detail-actions .logout-btn:active {
  transform: translateY(0);
}

/* 删除确认模态框特定样式 */
.delete-account-info {
  text-align: center;
}

.account-preview {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-large);
  margin-bottom: 24px;
  position: relative;
  overflow: hidden;
}

.account-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.account-preview .account-avatar {
  width: 56px;
  height: 56px;
  background: var(--primary-color);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 3px solid white;
}

.account-preview .account-avatar .avatar-img {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-round);
  object-fit: cover;
}

.account-preview .account-avatar .avatar-text {
  font-size: 20px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.account-preview .account-details {
  flex: 1;
  text-align: left;
}

.account-preview .account-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px;
  line-height: 1.3;
}

.account-preview .account-wxid {
  font-size: 13px;
  color: var(--text-secondary);
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  background: rgba(0, 0, 0, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.warning-message {
  background: linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%);
  border: 1px solid #ffec8b;
  border-left: 4px solid #fa8c16;
  border-radius: var(--radius-medium);
  padding: 20px;
  margin-bottom: 24px;
  position: relative;
}

.warning-message .warning-title {
  font-size: 16px;
  font-weight: 600;
  color: #d46b08;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.warning-message .warning-title::before {
  content: '⚠️';
  font-size: 18px;
}

.warning-message .warning-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.warning-message .warning-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  color: #d46b08;
  font-size: 14px;
  line-height: 1.5;
}

.warning-message .warning-list li i {
  color: #fa8c16;
  font-size: 16px;
  width: 16px;
  text-align: center;
  flex-shrink: 0;
}

.warning-message .warning-list li:last-child {
  padding-bottom: 0;
}

.warning-message .warning-icon {
  color: #fa8c16;
  font-size: 18px;
  margin-bottom: 8px;
}

.warning-message .warning-text {
  color: #d46b08;
  font-size: 14px;
  line-height: 1.5;
}

.warning-message .warning-text strong {
  font-weight: 600;
}

/* 模态框动画 */
.modal-overlay.fade-in {
  animation: modalFadeIn 0.3s ease;
}

.modal-overlay.fade-out {
  animation: modalFadeOut 0.3s ease;
}

.modal-container.fade-in {
  animation: modalSlideIn 0.3s ease;
}

.modal-container.fade-out {
  animation: modalSlideOut 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes modalSlideOut {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-20px);
    opacity: 0;
  }
}

/* 删除确认模态框响应式设计 */
@media (max-width: 768px) {
  .modal-container {
    max-width: 90vw;
    margin: 20px;
  }

  .account-preview {
    padding: 20px;
    gap: 16px;
  }

  .account-preview .account-avatar {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .account-preview .account-name {
    font-size: 16px;
  }

  .account-preview .account-wxid {
    font-size: 12px;
  }

  .warning-message {
    padding: 16px;
  }

  .warning-message .warning-list li {
    font-size: 13px;
  }

  .modal-footer {
    padding: 16px 20px;
    flex-direction: column-reverse;
  }

  .modal-footer .btn {
    width: 100%;
    margin: 0;
  }
}

@media (max-width: 480px) {
  .modal-container {
    max-width: 95vw;
    margin: 10px;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .account-preview {
    padding: 16px;
    gap: 12px;
  }

  .account-preview .account-avatar {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .modal-title {
    font-size: 16px;
  }
}

/* 配置模态框 */
.config-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.config-modal.show {
  display: flex;
}

/* 配置模态框内容 - 用于弹窗 */
.config-modal .config-content {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--shadow-heavy);
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-light);
}

.config-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: var(--radius-small);
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.config-body {
  padding: 24px;
}

.config-item {
  margin-bottom: 20px;
}

.config-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary);
}

.config-item input,
.config-item select {
  width: 100%;
  height: 36px;
  padding: 0 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
}

.config-item input:focus,
.config-item select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.config-item small {
  display: block;
  margin-top: 4px;
  color: var(--text-tertiary);
  font-size: 12px;
}

.config-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--border-light);
  text-align: right;
}

/* Toast通知 */
.toast-container {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 3000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toast {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-medium);
  min-width: 300px;
  animation: slideInRight 0.3s ease;
}

.toast.success {
  border-color: var(--success-color);
  background: rgba(82, 196, 26, 0.05);
}

.toast.error {
  border-color: var(--error-color);
  background: rgba(255, 77, 79, 0.05);
}

.toast.warning {
  border-color: var(--warning-color);
  background: rgba(250, 173, 20, 0.05);
}

.toast i {
  font-size: 16px;
}

.toast.success i {
  color: var(--success-color);
}

.toast.error i {
  color: var(--error-color);
}

.toast.warning i {
  color: var(--warning-color);
}

.toast span {
  font-size: 14px;
  color: var(--text-primary);
}

/* 加载动画 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.loading-spinner {
  text-align: center;
}

.loading-spinner i {
  font-size: 32px;
  color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.loading-spinner p {
  margin-top: 12px;
  font-size: 14px;
  color: var(--text-secondary);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-tertiary);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 18px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.empty-state p {
  font-size: 14px;
}

/* 重连按钮样式 */
.reconnect-buttons {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reconnect-row {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.reconnect-btn {
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: var(--radius-small);
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  min-width: 100px;
  justify-content: center;
}

.reconnect-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.reconnect-btn:active {
  transform: translateY(0);
}

.reconnect-btn i {
  font-size: 12px;
}

.reconnect-btn.btn-primary {
  background: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-color);
}

.reconnect-btn.btn-primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.reconnect-btn.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

.reconnect-btn.btn-secondary:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

.reconnect-btn.btn-success {
  background: var(--success-color);
  color: white;
  border: 1px solid var(--success-color);
}

.reconnect-btn.btn-success:hover {
  background: #059669;
  border-color: #059669;
}

.reconnect-btn.btn-info {
  background: #0ea5e9;
  color: white;
  border: 1px solid #0ea5e9;
}

.reconnect-btn.btn-info:hover {
  background: #0284c7;
  border-color: #0284c7;
}

/* 现代化刷新按钮加载状态样式 */
.btn.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.9;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--primary-light) !important;
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
}

.btn.loading:hover {
  transform: none !important;
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.15) !important;
}

/* 现代化按钮点击反馈效果 */
.btn-clicked {
  transform: scale(0.98);
  transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 现代化加载状态图标动画 */
.btn.loading .fa-spinner,
.btn.loading .fa-sync-alt {
  margin-right: 8px;
  color: currentColor;
  transform-origin: center;
}

/* 刷新按钮图标切换动画 */
.btn .fa-sync-alt {
  transition: transform 0.2s ease;
}

.btn:hover .fa-sync-alt {
  transform: rotate(180deg);
}

/* 禁用状态下的按钮样式 */
.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.btn:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
  border-color: var(--border-color) !important;
  color: var(--text-disabled) !important;
}

/* 现代化刷新按钮样式 - 与添加账号按钮保持一致 */
#refresh-accounts-overview {
  background: transparent !important;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  min-width: 100px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

#refresh-accounts-overview:hover {
  background: var(--primary-light) !important;
  border-color: var(--primary-hover);
  color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.15);
}

#refresh-accounts-overview:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 150, 255, 0.2);
}



/* 其他刷新按钮的统一现代化样式 */
#refresh-system-info,
#refresh-config {
  background: transparent !important;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  min-width: 100px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

#refresh-system-info:hover,
#refresh-config:hover {
  background: var(--primary-light) !important;
  border-color: var(--primary-hover);
  color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 150, 255, 0.15);
}

#refresh-system-info:active,
#refresh-config:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 150, 255, 0.2);
}

/* 重新生成设备信息按钮样式 */
#regenerate-all-device-info-btn {
  background: transparent !important;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
}

#regenerate-all-device-info-btn:hover {
  background: var(--primary-light) !important;
  border-color: var(--primary-hover);
  color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .btn.loading {
    transform: scale(0.96);
  }

  .btn-clicked {
    transform: scale(0.92);
  }

  .btn.loading .fa-spinner {
    margin-right: 6px;
  }
}

/* 减少动画偏好设置支持 */
@media (prefers-reduced-motion: reduce) {
  .btn.loading,
  .btn-clicked {
    transform: none !important;
    transition: opacity var(--transition-fast) !important;
  }

  .btn.loading .fa-spinner {
    animation: none !important;
  }
}
