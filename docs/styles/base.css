/* 现代化API文档样式 - 基础样式 */

/* 引入圆润字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@300;400;500;600;700;800&display=swap');

/* 强制主题应用 - 覆盖浏览器默认行为 */
html {
  color-scheme: light; /* 强制亮色主题为默认 */
}

html[data-theme="dark"] {
  color-scheme: dark; /* 只有明确设置时才使用暗色主题 */
}

/* 基础重置和变量 */
:root {
  /* 颜色系统 - 亮色主题 */
  --primary-color: #1890ff;
  --primary-hover: #40a9ff;
  --primary-light: rgba(24, 144, 255, 0.1);
  --success-color: #52c41a;
  --success-light: rgba(82, 196, 26, 0.1);
  --warning-color: #faad14;
  --warning-hover: #d48806;
  --warning-light: rgba(250, 173, 20, 0.1);
  --error-color: #ff4d4f;
  --error-hover: #d9363e;
  --error-light: rgba(255, 77, 79, 0.1);

  /* 文本颜色 - 亮色主题 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-disabled: #bfbfbf;

  /* 边框颜色 - 亮色主题 */
  --border-color: #d9d9d9;
  --border-light: #f0f0f0;
  --border-hover: var(--primary-color);

  /* 背景颜色 - 亮色主题 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-hover: #f8f9fa;
  --bg-code: #f6f8fa;

  /* 代码文本颜色 - 亮色主题 */
  --text-code: #24292f;

  /* 阴影系统 */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-focus: 0 0 0 2px rgba(24, 144, 255, 0.2);

  /* 圆角系统 */
  --radius-small: 6px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --radius-round: 50%;

  /* 布局尺寸 */
  --header-height: 64px;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 60px;
  --content-max-width: 1800px;

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* 动画 */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* 其他 */
  --skeleton-bg: #f5f5f5;
  --z-dropdown: 1000;
  --z-modal: 2000;
  --z-toast: 3000;
}

/* 暗色主题变量 - 只有明确设置data-theme="dark"时才应用 */
html[data-theme="dark"],
[data-theme="dark"] {
  /* 颜色系统 - 暗色主题 */
  --primary-color: #4096ff;
  --primary-hover: #69b1ff;
  --primary-light: rgba(64, 150, 255, 0.15);
  --success-color: #73d13d;
  --success-light: rgba(115, 209, 61, 0.15);
  --warning-color: #ffc53d;
  --warning-hover: #faad14;
  --warning-light: rgba(255, 197, 61, 0.15);
  --error-color: #ff7875;
  --error-hover: #ff4d4f;
  --error-light: rgba(255, 120, 117, 0.15);

  /* 文本颜色 - 暗色主题 */
  --text-primary: #ffffff;
  --text-secondary: #d9d9d9;
  --text-tertiary: #8c8c8c;
  --text-disabled: #595959;

  /* 边框颜色 - 暗色主题 */
  --border-color: #434343;
  --border-light: #303030;
  --border-hover: var(--primary-color);

  /* 背景颜色 - 暗色主题 */
  --bg-primary: #1f1f1f;
  --bg-secondary: #141414;
  --bg-tertiary: #262626;
  --bg-hover: #2a2a2a;
  --bg-code: #0d1117;

  /* 代码文本颜色 - 暗色主题 */
  --text-code: #e6edf3;

  /* 阴影系统 - 暗色主题 */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.25);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.35);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.45);
  --shadow-focus: 0 0 0 2px rgba(64, 150, 255, 0.3);

  /* 其他 - 暗色主题 */
  --skeleton-bg: #262626;
}

/* 主题切换过渡动画 */
* {
  transition: background-color var(--transition-normal),
              color var(--transition-normal),
              border-color var(--transition-normal),
              box-shadow var(--transition-normal);
}

/* 为某些元素禁用过渡以提高性能 */
*:before, *:after,
.loading-spinner,
.fa-spin {
  transition: none !important;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 防止横向滚动条并隐藏所有滚动条 */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none; /* Chrome, Safari 和 Opera */
}

/* 全局隐藏所有滚动条 */
*::-webkit-scrollbar {
  display: none; /* Chrome, Safari 和 Opera */
}

* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}

/* 确保所有容器都不会导致横向滚动 */
.container, .main-content, .content-area {
  max-width: 100%;
  overflow-x: hidden;
}

/* 模态框打开时的body样式 */
body.modal-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* 字体变量定义 */
:root {
  --font-primary: "SF Pro Rounded", "SF Compact Rounded", "SF UI Rounded",
    "Helvetica Rounded", "Arial Rounded MT Bold", "Nunito Sans", "Inter",
    "PingFang SC", "Hiragino Sans GB", "Source Han Sans SC", "Noto Sans CJK SC",
    "Microsoft YaHei UI", "Microsoft YaHei", "Segoe UI", -apple-system,
    BlinkMacSystemFont, sans-serif;
  --font-secondary: "Inter", "Nunito Sans", "SF Pro Rounded", "SF Compact Rounded",
    "PingFang SC", "Hiragino Sans GB", "Source Han Sans SC", "Microsoft YaHei UI",
    sans-serif;
  --font-mono: "Monaco", "Menlo", "Ubuntu Mono", "Consolas", "Courier New", monospace;
}

/* 全局字体渲染优化 */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 标题字体 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-secondary);
  font-weight: 600;
  letter-spacing: -0.02em;
}

/* 交互元素字体 */
button, .btn,
input, select, textarea,
.card, .modal, .sidebar {
  font-family: var(--font-secondary);
}

/* 特定元素字体权重 */
button, .btn {
  font-weight: 500;
  letter-spacing: -0.01em;
}

input, select, textarea {
  font-weight: 400;
}

/* 全局输入框样式重置和主题适配 */
input, select, textarea {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

input::placeholder, textarea::placeholder {
  color: var(--text-tertiary);
  opacity: 1; /* Firefox 兼容性 */
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-focus);
}

input:disabled, select:disabled, textarea:disabled {
  background: var(--bg-secondary);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.6;
}

/* 优化特定文本元素 */
.account-name, .account-wxid, .info-label, .info-value,
.qrcode-status, .qrcode-action-link,
.method-tab, .sidebar-item {
  font-family: var(--font-secondary);
  letter-spacing: -0.01em;
}

.account-name, .info-value {
  font-weight: 600;
}

.account-wxid {
  font-weight: 400;
}

.info-label {
  font-weight: 500;
}

body {
  font-family: var(--font-primary);
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  overflow-x: hidden;
  font-weight: 400;
}
