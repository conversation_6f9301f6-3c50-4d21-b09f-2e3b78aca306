/* 页面特定样式 */

/* 概览页面 */
.overview-header {
  text-align: center;
  margin-bottom: 48px;
}

.overview-header h1 {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.overview-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
  width: 100%;
}

.overview-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px;
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
}

.overview-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.card-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(24, 144, 255, 0.1);
  border-radius: var(--radius-medium);
  color: var(--primary-color);
  font-size: 20px;
  flex-shrink: 0;
}

.card-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.card-content p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 系统信息部分 */
.system-info-section {
  background: var(--bg-primary);
  padding: 32px;
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  margin-bottom: 48px;
}

.system-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.system-info-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-medium);
  padding: 20px;
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.system-info-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.system-info-card .info-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-color);
  border-radius: var(--radius-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.system-info-card .info-content {
  flex: 1;
}

.system-info-card .info-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 4px;
  font-weight: 500;
}

.system-info-card .info-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  transition: all 0.3s ease;
}

/* 动态更新效果 */
.system-info-card .info-value.updating {
  color: var(--primary-color);
  transform: scale(1.02);
}

/* 运行时间特殊样式 */
#system-uptime {
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

#system-uptime.updating {
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 统计部分 */
.stats-section {
  background: var(--bg-primary);
  padding: 32px;
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
}

.stats-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24px;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: var(--radius-medium);
  background: var(--bg-secondary);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 500;
}

/* API文档页面 */
.apis-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.apis-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
}

.apis-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-group {
  display: flex;
  gap: 12px;
}

.filter-group select {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 13px;
  cursor: pointer;
}

.filter-group select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.view-toggle {
  display: flex;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  overflow: hidden;
}

.view-btn {
  padding: 6px 12px;
  background: var(--bg-primary);
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn:hover,
.view-btn.active {
  background: var(--primary-color);
  color: white;
}

/* API卡片 */
.apis-content {
  display: grid;
  gap: 16px;
  width: 100%;
}

.apis-content.grid-view {
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
}

.apis-content.list-view {
  grid-template-columns: 1fr;
}

.api-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-medium);
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.api-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-light);
}

.api-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.method-badge {
  padding: 4px 8px;
  border-radius: var(--radius-small);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.method-badge.post {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.method-badge.get {
  background: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
}

.method-badge.put {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.method-badge.delete {
  background: rgba(255, 77, 79, 0.1);
  color: var(--error-color);
}

.api-path {
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 13px;
  color: var(--text-primary);
  font-weight: 500;
}

.api-card-body {
  margin-bottom: 16px;
}

.api-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.api-description {
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 13px;
}

.api-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid var(--border-light);
}

.api-tags {
  display: flex;
  gap: 6px;
}

.api-tag {
  padding: 2px 6px;
  background: var(--bg-secondary);
  color: var(--text-tertiary);
  border-radius: var(--radius-small);
  font-size: 11px;
  font-weight: 500;
}

.api-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}
